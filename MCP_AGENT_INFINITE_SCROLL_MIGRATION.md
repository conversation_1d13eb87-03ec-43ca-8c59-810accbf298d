# McpPage 和 AgentPage 无限滚动迁移说明

## 概述

参照 KnowledgePage 的实现，将 McpPage 和 AgentPage 的传统分页逻辑改为下拉滚动到底部加载下一页的无限滚动模式。

## 修改的文件

### 1. `src/features/qe_rag/mcp/McpPage.jsx`

**主要变更：**
- 移除了传统的分页组件 (`Pagination`)
- 移除了分页相关的状态管理 (`pagination`, `handlePageChange`, `loading`, `servers`)
- 重构了数据获取逻辑，创建了 `fetchServersData` 函数适配无限滚动
- 使用 `InfiniteScrollContainer` 组件替换原有的卡片列表渲染
- 添加了 `infiniteScrollRef` 用于控制无限滚动组件
- 重构了搜索和标签切换逻辑，使用 `updateParams` 和 `refresh` 方法
- 重构了 `handleSave` 和 `handleDelete` 函数，支持刷新回调

**具体修改：**
- 导入了 `InfiniteScrollContainer` 组件，移除了 `Pagination`, `Spin` 等不再需要的导入
- 简化了状态管理，移除了 `loading`, `servers`, `pagination` 状态
- 重构了 `handleTabChange` 和 `handleSearch` 函数
- 创建了 `renderServerCard` 函数，接收刷新方法作为参数
- 修改了 `handleDelete` 函数，支持传入刷新回调

### 2. `src/features/qe_rag/agent/AgentPage.jsx`

**主要变更：**
- 移除了传统的分页组件 (`Pagination`)
- 移除了分页相关的状态管理 (`agentList`, `loading`, `currentPage`, `total`)
- 重构了数据获取逻辑，创建了 `fetchAgentData` 函数适配无限滚动
- 使用 `InfiniteScrollContainer` 组件替换原有的卡片列表渲染
- 添加了 `infiniteScrollRef` 用于控制无限滚动组件
- 重构了搜索和标签切换逻辑，使用 `refresh` 方法

**具体修改：**
- 导入了 `InfiniteScrollContainer` 组件，移除了 `Pagination`, `Spin`, `Empty` 等不再需要的导入
- 简化了状态管理，移除了 `agentList`, `loading`, `currentPage`, `total` 状态
- 重构了 `handleTabChange` 和 `handleSearch` 函数
- 修改了 `renderAgentCard` 函数，使其适配无限滚动的参数格式

## 功能特性

### 1. 无限滚动
- 滚动到距离底部100px时自动加载下一页
- 支持自定义触发阈值
- 防止重复加载的保护机制

### 2. 状态管理
- 自动处理加载状态
- 错误状态处理和重试机制
- 空状态展示

### 3. 参数更新
- 支持搜索参数实时更新
- 标签切换时自动刷新数据
- 工作组筛选联动（仅McpPage）

### 4. 性能优化
- 使用 `useCallback` 优化函数重新创建
- 防抖滚动事件处理
- 引用缓存避免不必要的重新渲染

## 使用方法

### McpPage 无限滚动配置
```jsx
<InfiniteScrollContainer
    ref={infiniteScrollRef}
    fetchData={fetchServersData}
    renderItem={(server, index, actions) => renderServerCard(server, index, actions)}
    scrollOptions={{
        pageSize,
        initialParams: searchParams,
        threshold: 100
    }}
    className={styles.scrollableContent}
    gridClassName={styles.cardGrid}
    contentStyle={{ padding: '24px' }}
    renderEmpty={() => (
        <div className={styles.emptyContainer}>
            <div className={styles.emptyMessage}>暂无服务器数据</div>
        </div>
    )}
    loadingText="加载中..."
    noMoreText="没有更多服务器了"
/>
```

### AgentPage 无限滚动配置
```jsx
<InfiniteScrollContainer
    ref={infiniteScrollRef}
    fetchData={fetchAgentData}
    renderItem={(agent, index, actions) => renderAgentCard(agent, index, actions)}
    scrollOptions={{
        pageSize,
        initialParams: {},
        threshold: 100
    }}
    className={styles.scrollableContent}
    gridClassName={styles.cardGrid}
    contentStyle={{ padding: '24px' }}
    renderEmpty={() => (
        <div className={styles.emptyContainer}>
            <div className={styles.emptyMessage}>暂无智能体数据</div>
        </div>
    )}
    loadingText="加载中..."
    noMoreText="没有更多智能体了"
/>
```

## 兼容性说明

- 保持了原有的UI界面和交互逻辑
- 搜索、筛选、标签切换功能完全兼容
- 新建、编辑、删除操作正常工作（仅McpPage）
- 响应式设计保持不变

## 测试建议

### McpPage 测试项目
1. 测试基本的滚动加载功能
2. 测试搜索和工作组筛选功能
3. 测试标签切换功能（我的/全部）
4. 测试新建、编辑、删除服务器操作
5. 测试错误处理和重试机制
6. 测试空状态展示
7. 测试响应式布局

### AgentPage 测试项目
1. 测试基本的滚动加载功能
2. 测试搜索功能
3. 测试标签切换功能（我发布的/全部）
4. 测试智能体编辑跳转
5. 测试错误处理和重试机制
6. 测试空状态展示
7. 测试响应式布局

## 注意事项

1. **API兼容性**：确保后端API支持分页参数（page, size）
2. **数据格式**：确保API返回格式包含 `items`, `total`, `page` 字段
3. **权限控制**：McpPage中的编辑/删除权限检查保持不变
4. **样式适配**：需要确保CSS样式支持网格布局和无限滚动容器

## 后续优化建议

1. 添加虚拟滚动支持大量数据
2. 添加骨架屏提升用户体验
3. 支持预加载下一页数据
4. 添加滚动位置记忆功能
5. 支持自定义加载动画
6. 添加数据缓存机制
