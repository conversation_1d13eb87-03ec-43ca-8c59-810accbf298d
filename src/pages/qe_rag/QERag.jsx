import { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'umi';
import { Layout, message, Tooltip, Tabs } from 'antd';
import {
    TeamOutlined,
    HomeOutlined,
    BookOutlined,
    RobotOutlined,
    AppstoreOutlined
} from '@ant-design/icons';
import { stringifyUrl } from 'query-string';
import baseModel from 'COMMON/models/baseModel';
import { connectModel } from 'COMMON/middleware';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import { getAllGroupsList, getJoinedList } from 'COMMON/api/qe_rag/workgroup';
import { getEmbddingModels } from 'COMMON/api/qe_rag/model';
import LayoutHeader from 'LAYOUTS/BasicLayout/LayoutHeader';
import { getQueryParams } from 'COMMON/utils/utils';
import MemberManageModal from 'FEATURES/qe_rag/MemberManageModal/MemberManageModal.jsx';
import styles from './QERag.module.less';

function QeRagIndexPage(props) {
    const {
        joinedWorkGroupList,
        allWorkGroupList,
        setAllWorkGroupList,
        setJoinedWorkGroupList,
        embeddingModelList,
        setEmbeddingModelList,
        dispatch
    } = props;
    const location = useLocation();
    const navigate = useNavigate();
    const query = getQueryParams();
    const [messageApi, contextHolder] = message.useMessage();
    const [memberModalVisible, setMemberModalVisible] = useState(false);
    const [activeTab, setActiveTab] = useState('chat');



    // 根据当前路径设置活跃的 tab
    useEffect(() => {
        const pathname = location.pathname;
        if (pathname.includes('/chat')) {
            setActiveTab('chat');
        } else if (pathname.includes('/knowledge')) {
            setActiveTab('knowledge');
        } else if (pathname.includes('/agent')) {
            setActiveTab('agent');
        } else if (pathname.includes('/mcp')) {
            setActiveTab('mcp');
        }
    }, [location.pathname]);

    // Tab 切换处理函数
    const handleTabChange = (key) => {
        setActiveTab(key);
        const pathMap = {
            chat: '/qe_rag/chat',
            knowledge: '/qe_rag/knowledge',
            agent: '/qe_rag/agent',
            mcp: '/qe_rag/mcp'
        };
        navigate(pathMap[key]);
    };
    useEffect(() => {
        // 获取工作组列表
        const fetchWorkGroupData = async () => {
            // 获取所有工作组列表
            const allGroupsResult = await getAllGroupsList({});
            if (allGroupsResult) {
                setAllWorkGroupList(allGroupsResult);
            }

            // 获取已加入的工作组列表
            const joinedGroupsResult = await getJoinedList({});
            if (joinedGroupsResult) {
                setJoinedWorkGroupList(joinedGroupsResult);
            }
        };

        fetchWorkGroupData();
    }, []);
    useEffect(() => {
        // 获取切词模型
        const fetchEmbeddingModels = async () => {
            const embeddingModelsResult = await getEmbddingModels();
            if (embeddingModelsResult) {
                setEmbeddingModelList(embeddingModelsResult);
            }
        };

        fetchEmbeddingModels();
    }, []);
    // Tab 配置
    const tabItems = [
        {
            key: 'chat',
            label: (
                <span className={styles.tabItem}>
                    <HomeOutlined />
                    首页
                </span>
            )
        },
        {
            key: 'knowledge',
            label: (
                <span className={styles.tabItem}>
                    <BookOutlined />
                    知识库
                </span>
            )
        },
        {
            key: 'agent',
            label: (
                <span className={styles.tabItem}>
                    <RobotOutlined />
                    智能体
                </span>
            )
        },
        {
            key: 'mcp',
            label: (
                <span className={styles.tabItem}>
                    <AppstoreOutlined />
                    MCP市场
                </span>
            )
        }
    ];

    return (
        <Layout className={styles.layout}>
            <LayoutHeader
                showIcon={false}
                showRunSetting={false}
                showSetCenter={false}
                showSpaceSelect={false}
                showModuleSelect={false}
                onChange={() => {
                    let queryParams = {
                        moduleId: query?.moduleId ?? query?.productId
                    };
                    navigate(
                        stringifyUrl({
                            url: location.pathname,
                            query: queryParams
                        })
                    );
                }}
                centerExtra={
                    <div className={styles.headerCenter}>
                        <Tabs
                            activeKey={activeTab}
                            items={tabItems}
                            onChange={handleTabChange}
                            // onChange={setActiveTab}
                            className={styles.navTabs}
                            size="small"
                        />
                    </div>
                }
                rightExtra={
                    <Tooltip title="成员管理">
                        <TeamOutlined
                            className={styles.memberIcon}
                            onClick={() => setMemberModalVisible(true)}
                        />
                    </Tooltip>
                }
            />

            {/* <Layout className={styles.right}>
                <div
                    className={classnames(styles.rightLayout, {
                        [styles.intergrationRightLayout]: asider
                    })}
                >
                </div>
            </Layout> */}
            <Outlet key={query?.moduleId} />
            <MemberManageModal
                visible={memberModalVisible}
                onCancel={() => setMemberModalVisible(false)}
            />
            {contextHolder}
        </Layout>
    );
}

export default connectModel([ragModel, baseModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(QeRagIndexPage);
