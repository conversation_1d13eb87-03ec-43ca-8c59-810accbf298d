.container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 6px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
        background: #f5f5f5;
        border-color: #d9d9d9;
    }

    &.horizontal {
        flex-direction: row;
    }

    &.vertical {
        flex-direction: column;
        text-align: center;
    }

    &.small {
        padding: 4px 6px;
        gap: 6px;
    }

    &.large {
        padding: 12px;
        gap: 12px;
    }
}

.avatar {
    flex-shrink: 0;
}

.userDetails {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
    flex: 1;

    .vertical & {
        align-items: center;
    }
}

.username {
    font-size: 14px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .small & {
        font-size: 12px;
    }

    .large & {
        font-size: 16px;
    }
}

.department {
    font-size: 12px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .small & {
        font-size: 11px;
    }

    .large & {
        font-size: 13px;
    }
}

.refreshIcon {
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:hover {
        color: #1890ff;
        background: #e6f7ff;
    }

    &:active {
        transform: rotate(180deg);
    }
}
