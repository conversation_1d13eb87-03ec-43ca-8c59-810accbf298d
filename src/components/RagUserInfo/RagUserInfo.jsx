import React from 'react';
import { Spin, Alert, Avatar, Typography } from 'antd';
import { UserOutlined, ReloadOutlined } from '@ant-design/icons';
import useRagAuth from 'HOOKS/useRagAuth';
import styles from './RagUserInfo.module.less';

const { Text } = Typography;

/**
 * RAG用户信息组件
 * 在RAG相关页面显示当前登录用户信息
 */
const RagUserInfo = ({ 
    showDepartment = true, 
    showRefresh = true, 
    size = 'default',
    layout = 'horizontal' // horizontal | vertical
}) => {
    const {
        ragUserInfo,
        ragUserLoading,
        isRagAuthenticated,
        isRagRoute,
        ragUsername,
        ragUserImage,
        ragUserDepartment,
        refreshRagUser
    } = useRagAuth();

    // 如果不是RAG路由，不显示组件
    if (!isRagRoute) {
        return null;
    }

    // 加载状态
    if (ragUserLoading) {
        return (
            <div className={styles.container}>
                <Spin size="small" />
                <Text type="secondary">获取用户信息中...</Text>
            </div>
        );
    }

    // 未认证状态
    if (!isRagAuthenticated || !ragUserInfo) {
        return (
            <Alert
                message="未获取到RAG用户信息"
                description="请检查网络连接或联系管理员"
                type="warning"
                size="small"
                showIcon
                action={
                    showRefresh && (
                        <ReloadOutlined 
                            onClick={refreshRagUser}
                            className={styles.refreshIcon}
                            title="重新获取用户信息"
                        />
                    )
                }
            />
        );
    }

    // 已认证状态
    return (
        <div className={`${styles.container} ${styles[layout]} ${styles[size]}`}>
            <Avatar
                src={ragUserImage}
                icon={<UserOutlined />}
                size={size === 'small' ? 24 : size === 'large' ? 40 : 32}
                className={styles.avatar}
            />
            <div className={styles.userDetails}>
                <Text strong className={styles.username}>
                    {ragUsername}
                </Text>
                {showDepartment && ragUserDepartment && (
                    <Text type="secondary" className={styles.department}>
                        {ragUserDepartment}
                    </Text>
                )}
            </div>
            {showRefresh && (
                <ReloadOutlined 
                    onClick={refreshRagUser}
                    className={styles.refreshIcon}
                    title="刷新用户信息"
                />
            )}
        </div>
    );
};

export default RagUserInfo;
