import { Card, Button, Descriptions, Tag, Space } from 'antd';
import { ReloadOutlined, ClearOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import styles from './ReduxRagTest.module.less';

/**
 * Redux RAG状态管理测试组件
 */
function ReduxRagTest(props) {
    const {
        ragUserInfo,
        ragUsername,
        ragUserImage,
        ragUserDepartment,
        getRagUserInfo,
        clearRagUserInfo
    } = props;

    const isRagRoute = window.location.pathname.includes('/qe_rag');
    const isRagAuthenticated = ragUserInfo !== null;

    const handleGetRagUser = async () => {
        try {
            await getRagUserInfo();
        } catch (error) {
            console.error('获取RAG用户信息失败:', error);
        }
    };

    const handleClearRagUser = () => {
        clearRagUserInfo();
    };

    return (
        <div className={styles.container}>
            <Card title="Redux RAG状态管理测试" className={styles.testCard}>
                {/* 当前状态 */}
                <Card title="当前状态" size="small" style={{ marginBottom: 16 }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                            <Tag color={isRagRoute ? 'green' : 'default'}>
                                {isRagRoute ? '当前为RAG路由' : '当前为非RAG路由'}
                            </Tag>
                        </div>
                        <div>
                            <Tag color={isRagAuthenticated ? 'green' : 'red'}>
                                {isRagAuthenticated ? 'RAG已认证' : 'RAG未认证'}
                            </Tag>
                        </div>
                    </Space>
                </Card>

                {/* Redux状态信息 */}
                <Card title="Redux状态信息" size="small" style={{ marginBottom: 16 }}>
                    {ragUserInfo ? (
                        <Descriptions column={1} size="small">
                            <Descriptions.Item label="ragUsername">{ragUsername}</Descriptions.Item>
                            <Descriptions.Item label="ragUserDepartment">{ragUserDepartment}</Descriptions.Item>
                            <Descriptions.Item label="ragUserImage">
                                <div style={{ wordBreak: 'break-all', fontSize: '12px' }}>
                                    {ragUserImage}
                                </div>
                            </Descriptions.Item>
                            <Descriptions.Item label="完整ragUserInfo">
                                <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                                    {JSON.stringify(ragUserInfo, null, 2)}
                                </pre>
                            </Descriptions.Item>
                        </Descriptions>
                    ) : (
                        <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                            暂无RAG用户信息
                        </div>
                    )}
                </Card>

                {/* 操作按钮 */}
                <Card title="Redux Actions测试" size="small">
                    <Space>
                        <Button 
                            type="primary" 
                            icon={<ReloadOutlined />} 
                            onClick={handleGetRagUser}
                        >
                            获取RAG用户信息 (getRagUserInfo)
                        </Button>
                        <Button 
                            icon={<ClearOutlined />} 
                            onClick={handleClearRagUser}
                            disabled={!isRagAuthenticated}
                        >
                            清除RAG用户信息 (clearRagUserInfo)
                        </Button>
                    </Space>
                </Card>

                {/* 说明 */}
                <Card title="说明" size="small" style={{ marginTop: 16 }}>
                    <Space direction="vertical" size="small">
                        <div>• ragUsername现在存储在Redux状态管理中 (baseModel)</div>
                        <div>• PersonalCenter组件会自动从Redux获取RAG用户信息</div>
                        <div>• 在RAG路由下，BasicLayout会自动调用getRagUserInfo</div>
                        <div>• 在非RAG路由下，会自动调用clearRagUserInfo</div>
                    </Space>
                </Card>
            </Card>
        </div>
    );
}

export default connectModel([baseModel], (state) => ({
    ragUserInfo: state.common.base.ragUserInfo,
    ragUsername: state.common.base.ragUsername,
    ragUserImage: state.common.base.ragUserImage,
    ragUserDepartment: state.common.base.ragUserDepartment
}))(ReduxRagTest);
