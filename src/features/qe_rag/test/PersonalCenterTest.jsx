import { Card, Descriptions, Tag, Space, Button } from 'antd';
import { UserOutlined, ReloadOutlined } from '@ant-design/icons';
import useRagAuth from 'HOOKS/useRagAuth';
import PersonalCenter from 'LAYOUTS/BasicLayout/LayoutHeader/components/PersonalCenter';
import styles from './PersonalCenterTest.module.less';

/**
 * PersonalCenter RAG集成测试组件
 * 用于测试PersonalCenter组件在RAG路由下的表现
 */
const PersonalCenterTest = () => {
    const {
        isRagRoute,
        isRagAuthenticated,
        ragUserInfo,
        ragUserLoading,
        ragUsername,
        ragUserImage,
        ragUserDepartment,
        refreshRagUser
    } = useRagAuth();

    return (
        <div className={styles.container}>
            <Card title="PersonalCenter RAG集成测试" className={styles.testCard}>
                {/* 当前状态 */}
                <Card title="当前状态" size="small" style={{ marginBottom: 16 }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <div>
                            <Tag color={isRagRoute ? 'green' : 'default'}>
                                {isRagRoute ? '当前为RAG路由' : '当前为非RAG路由'}
                            </Tag>
                        </div>
                        <div>
                            <Tag color={isRagAuthenticated ? 'green' : 'red'}>
                                {isRagAuthenticated ? 'RAG已认证' : 'RAG未认证'}
                            </Tag>
                        </div>
                        <div>
                            <Tag color={ragUserLoading ? 'blue' : 'default'}>
                                {ragUserLoading ? '加载中' : '空闲'}
                            </Tag>
                        </div>
                    </Space>
                </Card>

                {/* RAG用户信息 */}
                <Card title="RAG用户信息" size="small" style={{ marginBottom: 16 }}>
                    {ragUserInfo ? (
                        <Descriptions column={1} size="small">
                            <Descriptions.Item label="用户名">{ragUsername}</Descriptions.Item>
                            <Descriptions.Item label="部门">{ragUserDepartment}</Descriptions.Item>
                            <Descriptions.Item label="头像">
                                {ragUserImage ? (
                                    <img 
                                        src={ragUserImage} 
                                        alt={ragUsername}
                                        style={{ width: 32, height: 32, borderRadius: '50%' }}
                                    />
                                ) : (
                                    <UserOutlined style={{ fontSize: 32 }} />
                                )}
                            </Descriptions.Item>
                        </Descriptions>
                    ) : (
                        <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                            <UserOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                            <div>暂无RAG用户信息</div>
                            <Button 
                                type="link" 
                                icon={<ReloadOutlined />}
                                onClick={refreshRagUser}
                                loading={ragUserLoading}
                            >
                                重新获取
                            </Button>
                        </div>
                    )}
                </Card>

                {/* PersonalCenter组件预览 */}
                <Card title="PersonalCenter组件预览" size="small" style={{ marginBottom: 16 }}>
                    <div className={styles.personalCenterPreview}>
                        <div className={styles.previewLabel}>
                            当前PersonalCenter显示效果：
                        </div>
                        <div className={styles.previewContainer}>
                            <PersonalCenter />
                        </div>
                    </div>
                </Card>

                {/* 说明信息 */}
                <Card title="说明" size="small">
                    <Space direction="vertical" size="small">
                        <div>• 在RAG路由下，PersonalCenter会显示RAG用户信息</div>
                        <div>• 在非RAG路由下，PersonalCenter会显示普通用户信息</div>
                        <div>• 用户头像会根据RAG用户信息动态切换</div>
                        <div>• 下拉菜单中会显示用户部门和RAG模式标识</div>
                        <div>• 头像旁边会显示"RAG"标识（仅在RAG路由下）</div>
                    </Space>
                </Card>
            </Card>
        </div>
    );
};

export default PersonalCenterTest;
