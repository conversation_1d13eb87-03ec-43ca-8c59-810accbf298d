import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import InfiniteScrollContainer from './InfiniteScrollContainer';

// Mock数据
const mockData = Array.from({ length: 50 }, (_, index) => ({
    id: index + 1,
    name: `Item ${index + 1}`,
    description: `Description for item ${index + 1}`
}));

// Mock fetch函数
const mockFetchData = jest.fn(({ page, size }) => {
    const start = (page - 1) * size;
    const end = start + size;
    const items = mockData.slice(start, end);
    
    return Promise.resolve({
        items,
        total: mockData.length,
        page
    });
});

// 渲染项目的函数
const renderItem = (item, index, { refresh }) => (
    <div key={item.id} data-testid={`item-${item.id}`}>
        <h3>{item.name}</h3>
        <p>{item.description}</p>
        <button onClick={refresh}>Refresh</button>
    </div>
);

describe('InfiniteScrollContainer', () => {
    beforeEach(() => {
        mockFetchData.mockClear();
    });

    test('renders initial data correctly', async () => {
        render(
            <InfiniteScrollContainer
                fetchData={mockFetchData}
                renderItem={renderItem}
                scrollOptions={{ pageSize: 10 }}
            />
        );

        // 等待初始数据加载
        await waitFor(() => {
            expect(screen.getByTestId('item-1')).toBeInTheDocument();
        });

        // 检查是否渲染了正确数量的项目
        expect(screen.getAllByTestId(/^item-/)).toHaveLength(10);
        
        // 检查是否调用了fetch函数
        expect(mockFetchData).toHaveBeenCalledWith({
            page: 1,
            size: 10
        });
    });

    test('loads more data on scroll', async () => {
        const { container } = render(
            <InfiniteScrollContainer
                fetchData={mockFetchData}
                renderItem={renderItem}
                scrollOptions={{ pageSize: 10, threshold: 0 }}
            />
        );

        // 等待初始数据加载
        await waitFor(() => {
            expect(screen.getByTestId('item-1')).toBeInTheDocument();
        });

        // 模拟滚动到底部
        const scrollContainer = container.firstChild;
        Object.defineProperty(scrollContainer, 'scrollTop', { value: 1000, writable: true });
        Object.defineProperty(scrollContainer, 'scrollHeight', { value: 1000, writable: true });
        Object.defineProperty(scrollContainer, 'clientHeight', { value: 500, writable: true });

        fireEvent.scroll(scrollContainer);

        // 等待加载更多数据
        await waitFor(() => {
            expect(mockFetchData).toHaveBeenCalledWith({
                page: 2,
                size: 10
            });
        });
    });

    test('shows empty state when no data', async () => {
        const emptyFetchData = jest.fn(() => Promise.resolve({
            items: [],
            total: 0,
            page: 1
        }));

        render(
            <InfiniteScrollContainer
                fetchData={emptyFetchData}
                renderItem={renderItem}
                renderEmpty={() => <div data-testid="empty-state">No data</div>}
            />
        );

        await waitFor(() => {
            expect(screen.getByTestId('empty-state')).toBeInTheDocument();
        });
    });

    test('shows error state on fetch failure', async () => {
        const errorFetchData = jest.fn(() => Promise.reject(new Error('Fetch failed')));

        render(
            <InfiniteScrollContainer
                fetchData={errorFetchData}
                renderItem={renderItem}
            />
        );

        await waitFor(() => {
            expect(screen.getByText('加载失败，请重试')).toBeInTheDocument();
        });
    });
});
