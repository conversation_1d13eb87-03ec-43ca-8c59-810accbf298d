import React, { useState, useRef } from 'react';
import { Card, Input, Button, Select } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import InfiniteScrollContainer from './InfiniteScrollContainer';

// 模拟数据
const generateMockData = (count = 100) => {
    return Array.from({ length: count }, (_, index) => ({
        id: index + 1,
        name: `项目 ${index + 1}`,
        description: `这是第 ${index + 1} 个项目的描述信息`,
        category: ['技术', '产品', '设计', '运营'][index % 4],
        createTime: new Date(Date.now() - Math.random() * 10000000000).toISOString().split('T')[0]
    }));
};

const mockData = generateMockData(100);

// 模拟API调用
const mockFetchData = async ({ page, size, name = '', category = '' }) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 过滤数据
    let filteredData = mockData;
    if (name) {
        filteredData = filteredData.filter(item => 
            item.name.toLowerCase().includes(name.toLowerCase())
        );
    }
    if (category) {
        filteredData = filteredData.filter(item => item.category === category);
    }
    
    // 分页
    const start = (page - 1) * size;
    const end = start + size;
    const items = filteredData.slice(start, end);
    
    return {
        items,
        total: filteredData.length,
        page
    };
};

// 渲染单个卡片
const renderCard = (item, index, { refresh, updateParams }) => (
    <Card
        key={item.id}
        style={{ marginBottom: 16 }}
        title={item.name}
        extra={
            <Button 
                size="small" 
                onClick={(e) => {
                    e.stopPropagation();
                    refresh();
                }}
            >
                刷新
            </Button>
        }
    >
        <p>{item.description}</p>
        <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
            <span>分类: {item.category}</span>
            <span>创建时间: {item.createTime}</span>
        </div>
    </Card>
);

// 演示组件
const InfiniteScrollDemo = () => {
    const [searchName, setSearchName] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const scrollRef = useRef(null);

    const handleSearch = () => {
        scrollRef.current?.updateParams({
            name: searchName,
            category: selectedCategory
        });
    };

    const handleCategoryChange = (value) => {
        setSelectedCategory(value);
        scrollRef.current?.updateParams({
            name: searchName,
            category: value
        });
    };

    const handleRefresh = () => {
        scrollRef.current?.refresh();
    };

    return (
        <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
            {/* 搜索栏 */}
            <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
                <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                    <Input
                        placeholder="搜索项目名称..."
                        prefix={<SearchOutlined />}
                        value={searchName}
                        onChange={(e) => setSearchName(e.target.value)}
                        onPressEnter={handleSearch}
                        style={{ width: 300 }}
                    />
                    <Select
                        placeholder="选择分类"
                        value={selectedCategory || undefined}
                        onChange={handleCategoryChange}
                        allowClear
                        style={{ width: 150 }}
                    >
                        <Select.Option value="技术">技术</Select.Option>
                        <Select.Option value="产品">产品</Select.Option>
                        <Select.Option value="设计">设计</Select.Option>
                        <Select.Option value="运营">运营</Select.Option>
                    </Select>
                    <Button onClick={handleSearch}>搜索</Button>
                    <Button onClick={handleRefresh}>刷新</Button>
                </div>
            </div>

            {/* 无限滚动容器 */}
            <InfiniteScrollContainer
                ref={scrollRef}
                fetchData={mockFetchData}
                renderItem={renderCard}
                scrollOptions={{
                    pageSize: 10,
                    initialParams: { name: '', category: '' },
                    threshold: 100
                }}
                contentStyle={{ padding: '16px' }}
                renderEmpty={() => (
                    <div style={{ textAlign: 'center', padding: '60px' }}>
                        <p style={{ fontSize: '16px', color: '#999' }}>暂无数据</p>
                    </div>
                )}
                loadingText="加载中..."
                noMoreText="已加载全部数据"
            />
        </div>
    );
};

export default InfiniteScrollDemo;
