# InfiniteScrollContainer 无限滚动容器组件

一个可复用的无限滚动容器组件，支持下拉滚动到底部时自动加载下一页数据。

## 特性

- 🚀 自动检测滚动到底部并加载下一页
- 🎯 可配置的触发阈值
- 🔄 支持刷新和参数更新
- 🎨 可自定义加载指示器和空状态
- 📱 响应式设计
- 🛠 TypeScript 友好
- ⚡ 性能优化，防止重复请求

## 基本用法

```jsx
import InfiniteScrollContainer from 'COMMON/components/InfiniteScrollContainer';

// 数据获取函数
const fetchData = async ({ page, size, ...otherParams }) => {
    const response = await api.getData({ page, size, ...otherParams });
    return {
        items: response.data,
        total: response.total,
        page: response.page
    };
};

// 渲染单个项目
const renderItem = (item, index, { refresh, updateParams }) => (
    <div key={item.id}>
        <h3>{item.name}</h3>
        <p>{item.description}</p>
        <button onClick={refresh}>刷新</button>
    </div>
);

function MyComponent() {
    return (
        <InfiniteScrollContainer
            fetchData={fetchData}
            renderItem={renderItem}
            scrollOptions={{
                pageSize: 20,
                initialParams: { category: 'all' },
                threshold: 100
            }}
        />
    );
}
```

## 高级用法

```jsx
import { useRef } from 'react';
import InfiniteScrollContainer from 'COMMON/components/InfiniteScrollContainer';

function AdvancedComponent() {
    const scrollRef = useRef(null);
    
    const handleSearch = (searchTerm) => {
        // 更新搜索参数并刷新数据
        scrollRef.current?.updateParams({ search: searchTerm });
    };
    
    const handleRefresh = () => {
        // 手动刷新数据
        scrollRef.current?.refresh();
    };

    return (
        <div>
            <input onChange={(e) => handleSearch(e.target.value)} />
            <button onClick={handleRefresh}>刷新</button>
            
            <InfiniteScrollContainer
                ref={scrollRef}
                fetchData={fetchData}
                renderItem={renderItem}
                gridClassName="my-grid-class"
                renderEmpty={() => <div>暂无数据</div>}
                renderError={(error, retry) => (
                    <div>
                        <p>加载失败: {error.message}</p>
                        <button onClick={retry}>重试</button>
                    </div>
                )}
                loadingText="正在加载..."
                noMoreText="已加载全部数据"
            />
        </div>
    );
}
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| fetchData | `(params) => Promise<{items, total, page}>` | - | 数据获取函数 |
| renderItem | `(item, index, actions) => ReactNode` | - | 渲染单个项目的函数 |
| renderEmpty | `() => ReactNode` | - | 渲染空状态的函数 |
| renderError | `(error, retry) => ReactNode` | - | 渲染错误状态的函数 |
| scrollOptions | `Object` | `{}` | 滚动配置选项 |
| className | `string` | `''` | 容器额外的CSS类名 |
| gridClassName | `string` | `''` | 内容区域的CSS类名 |
| containerStyle | `Object` | `{}` | 容器样式 |
| contentStyle | `Object` | `{}` | 内容区域样式 |
| loadingText | `string` | `'加载中...'` | 加载文本 |
| noMoreText | `string` | `'没有更多数据了'` | 没有更多数据的文本 |
| showLoadingIndicator | `boolean` | `true` | 是否显示加载指示器 |
| loadingIndicator | `ReactNode` | - | 自定义加载指示器 |
| noMoreIndicator | `ReactNode` | - | 自定义没有更多数据指示器 |

### scrollOptions

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| pageSize | `number` | `20` | 每页数据量 |
| threshold | `number` | `100` | 触发加载的距离阈值（像素） |
| enabled | `boolean` | `true` | 是否启用无限滚动 |
| initialParams | `Object` | `{}` | 初始查询参数 |

### Ref Methods

| 方法 | 说明 |
|------|------|
| refresh(newParams?) | 刷新数据，可选传入新参数 |
| updateParams(newParams) | 更新查询参数并刷新数据 |

### renderItem 函数参数

| 参数 | 类型 | 说明 |
|------|------|------|
| item | `any` | 当前项目数据 |
| index | `number` | 项目索引 |
| actions | `Object` | 操作方法对象 |
| actions.refresh | `Function` | 刷新数据的方法 |
| actions.updateParams | `Function` | 更新参数的方法 |

## 注意事项

1. `fetchData` 函数必须返回包含 `items`、`total` 和 `page` 字段的对象
2. 组件会自动处理加载状态和错误状态
3. 滚动容器需要有固定的高度才能正常工作
4. 建议在 `renderItem` 中使用 `useCallback` 优化性能
5. 组件内部使用了防抖机制，避免频繁触发加载
