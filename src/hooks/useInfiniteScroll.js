import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * 无限滚动Hook
 * @param {Function} fetchData - 获取数据的函数，接收参数 { page, size, ...otherParams }
 * @param {Object} options - 配置选项
 * @param {number} options.pageSize - 每页数据量，默认20
 * @param {number} options.threshold - 触发加载的距离阈值（像素），默认100
 * @param {boolean} options.enabled - 是否启用无限滚动，默认true
 * @param {Object} options.initialParams - 初始查询参数
 * @returns {Object} 返回状态和方法
 */
const useInfiniteScroll = (fetchData, options = {}) => {
    const {
        pageSize = 20,
        threshold = 100,
        enabled = true,
        initialParams = {}
    } = options;

    // 状态管理
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [error, setError] = useState(null);
    const [params, setParams] = useState(initialParams);

    // 引用
    const containerRef = useRef(null);
    const isLoadingRef = useRef(false);

    // 加载数据
    const loadData = useCallback(async (currentPage, currentParams, isRefresh = false) => {
        if (isLoadingRef.current) return;
        
        isLoadingRef.current = true;
        setLoading(true);
        setError(null);

        try {
            const response = await fetchData({
                page: currentPage,
                size: pageSize,
                ...currentParams
            });

            const newItems = response.items || [];
            const totalCount = response.total || 0;

            if (isRefresh) {
                setData(newItems);
            } else {
                setData(prev => [...prev, ...newItems]);
            }

            setTotal(totalCount);
            setHasMore(newItems.length === pageSize && (currentPage * pageSize) < totalCount);
        } catch (err) {
            setError(err);
            console.error('Failed to load data:', err);
        } finally {
            setLoading(false);
            isLoadingRef.current = false;
        }
    }, [fetchData, pageSize]);

    // 加载下一页
    const loadMore = useCallback(() => {
        if (!hasMore || loading || !enabled) return;
        
        const nextPage = page + 1;
        setPage(nextPage);
        loadData(nextPage, params);
    }, [hasMore, loading, enabled, page, params, loadData]);

    // 刷新数据（重置到第一页）
    const refresh = useCallback((newParams = {}) => {
        const updatedParams = { ...params, ...newParams };
        setParams(updatedParams);
        setPage(1);
        setData([]);
        setHasMore(true);
        setError(null);
        loadData(1, updatedParams, true);
    }, [params, loadData]);

    // 滚动事件处理
    const handleScroll = useCallback(() => {
        if (!containerRef.current || !enabled || loading || !hasMore) return;

        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        const distanceToBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceToBottom <= threshold) {
            loadMore();
        }
    }, [enabled, loading, hasMore, threshold, loadMore]);

    // 监听滚动事件
    useEffect(() => {
        const container = containerRef.current;
        if (!container || !enabled) return;

        container.addEventListener('scroll', handleScroll, { passive: true });
        return () => {
            container.removeEventListener('scroll', handleScroll);
        };
    }, [handleScroll, enabled]);

    // 初始化加载
    useEffect(() => {
        if (enabled) {
            loadData(1, initialParams, true);
        }
    }, [enabled, loadData, initialParams]); // 监听enabled和initialParams变化

    return {
        // 数据状态
        data,
        loading,
        hasMore,
        total,
        error,
        page,
        
        // 方法
        refresh,
        loadMore,
        
        // 引用
        containerRef,
        
        // 工具方法
        updateParams: (newParams) => {
            refresh(newParams);
        }
    };
};

export default useInfiniteScroll;
