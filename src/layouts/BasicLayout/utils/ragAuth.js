import axios from 'axios';

// RAG专用的HTTP客户端配置
const ragHttp = axios.create({
    baseURL: '/',
    timeout: 5000,
    withCredentials: true, // 重要：允许跨域携带 cookie
    headers: {
        'Content-Type': 'application/json'
    }
});

// 添加请求拦截器
ragHttp.interceptors.request.use(
    (config) => {
        config.headers['originalUrl'] = window.location.origin;
        return config;
    },
    (err) => {
        return Promise.reject(err);
    }
);

// 添加响应拦截器
ragHttp.interceptors.response.use(
    (res) => {
        const data = res.data;
        if (data.code !== 200 && data.code !== 40001) {
            console.error('RAG API Error:', data['message']);
        }
        return data;
    },
    (err) => {
        console.error('RAG API Request Failed:', err.response?.data?.message || `${err.response?.config?.url} 请求失败`);
        return Promise.reject(err);
    }
);

/**
 * 检查路由是否为RAG相关路由
 * @param {string} pathname - 当前路径
 * @returns {boolean} 是否为RAG路由
 */
export const isRagRoute = (pathname) => {
    return pathname.includes('/qe_rag');
};

/**
 * 获取RAG用户信息
 * @returns {Promise<Object>} 用户信息
 */
export const getRagUserInfo = async () => {
    try {
        const res = await ragHttp.get('/rag/api/user/current');
        if (res.code === 40001) {
            // 如果需要重定向，执行重定向
            if (res.redirectUrl) {
                window.location = res.redirectUrl;
                return null;
            }
        } else if (res.code === 200 && res.data) {
            // 存储用户信息到sessionStorage
            const userInfo = {
                username: res.data.name,
                imageUrl: res.data.hiImageUrl,
                departmentName: res.data.departmentName
            };
            
            sessionStorage.setItem('rag_username', userInfo.username);
            sessionStorage.setItem('rag_imageUrl', userInfo.imageUrl);
            sessionStorage.setItem('rag_departmentName', userInfo.departmentName);
            
            return userInfo;
        }
    } catch (error) {
        console.error('获取RAG用户信息失败:', error);
        // 如果捕获错误，使用默认用户信息
        const defaultUserInfo = {
            username: 'guanlin',
            imageUrl: 'https://erp.baidu.com/avatar/getAvatar?appCode=ERP&uuap=guanlin&token=QNSAMXOMTN',
            departmentName: '未知部门'
        };
        
        sessionStorage.setItem('rag_username', defaultUserInfo.username);
        sessionStorage.setItem('rag_imageUrl', defaultUserInfo.imageUrl);
        sessionStorage.setItem('rag_departmentName', defaultUserInfo.departmentName);
        
        return defaultUserInfo;
    }
    return null;
};

/**
 * 从sessionStorage获取RAG用户信息
 * @returns {Object|null} 用户信息
 */
export const getRagUserFromStorage = () => {
    const username = sessionStorage.getItem('rag_username');
    const imageUrl = sessionStorage.getItem('rag_imageUrl');
    const departmentName = sessionStorage.getItem('rag_departmentName');
    
    if (username && username !== 'undefined') {
        return {
            username,
            imageUrl,
            departmentName
        };
    }
    return null;
};

/**
 * 清除RAG用户信息
 */
export const clearRagUserInfo = () => {
    sessionStorage.removeItem('rag_username');
    sessionStorage.removeItem('rag_imageUrl');
    sessionStorage.removeItem('rag_departmentName');
};

/**
 * 检查RAG用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isRagUserLoggedIn = () => {
    const username = sessionStorage.getItem('rag_username');
    return username && username !== 'undefined';
};

/**
 * RAG路由守卫 - 确保RAG路由下有用户信息
 * @param {string} pathname - 当前路径
 * @returns {Promise<Object|null>} 用户信息或null
 */
export const ragRouteGuard = async (pathname) => {
    if (!isRagRoute(pathname)) {
        return null; // 非RAG路由，不处理
    }
    
    // 检查是否已有用户信息
    const existingUser = getRagUserFromStorage();
    if (existingUser) {
        return existingUser;
    }
    
    // 没有用户信息，尝试获取
    return await getRagUserInfo();
};
