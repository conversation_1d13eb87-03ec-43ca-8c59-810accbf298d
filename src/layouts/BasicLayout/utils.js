// 版本号对比
export const versionCmp = (updateV, curV) => {
    if (updateV.length !== 3 || curV.length !== 3) {
        return false;
    }
    // 第一位
    if (updateV[0] > curV[0]) {
        return true;
    }
    // 第二位
    if (updateV[0] === curV[0] && updateV[1] > curV[1]) {
        return true;
    }
    // 第三位
    if (updateV[0] === curV[0] && updateV[1] === curV[1] && updateV[2] > curV[2]) {
        return true;
    }
    return false;
};

// 检查是否为Electron环境
export const isElectron = () => {
    return typeof window !== 'undefined' && window.electron;
};
