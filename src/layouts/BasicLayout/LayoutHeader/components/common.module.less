.userInfo {
    margin: 0 5px;
    font-size: 12px;
    color: #777;
    cursor: pointer;

    .userInfoIcon {
        font-size: 14px;
    }

    .userAvatar {
        border: 1px solid #d9d9d9;
    }
}

.personalCenterIcon {
    font-size: 12px;
    margin-right: 5px;
}

.personalCenterItem {
    font-size: 12px;
    display: flex;
    align-items: center;
}



.layoutHeader {
    position: relative;
    padding: 0 20px;
    background-color: var(--layout-background-color) !important;
    border: none !important;
}

.normalLayoutHeader {
    border-bottom: 1px solid var(--border-color) !important;
    background-color: var(--background-color) !important;
}

.selectTag {
    font-size: 12px;
    color: var(--color2);
}

.titleName {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 15px;
    cursor: default;
    margin-left: 5px;
    padding: 0 11px 0 5px;
}

.divider {
    height: 16px;
}


.logo {
    margin-left: 10px;
}

.moduleSelect {
    font-size: 14px !important;
    color: #000 !important;
    background-color: var(--layout-background-color) !important;
    margin-bottom: 5px;
    margin-left: -10px;

    :global {

        .ant-select-selection-item,
        .custom-default-select-selection-item,
        .custom-dark-select-selection-item {
            font-weight: bold;
            font-size: 14px !important;
            color: #000 !important;
        }
    }
}

.rightIcon {
    margin-left: 5px;
    font-size: 12px;
    color: #b6b8bb;
}

.divider {
    float: left;
    height: 20px;
    line-height: 20px;
    color: #eee;
}

.foldIcon {
    margin-left: 10px;
}

.rightExtra {
    position: absolute;
    right: 15px;
}

.search {
    margin-left: 8px;
    width: 70px !important;
}


.taskItemIcon {
    margin-left: 5px;
}

// setting menu

.menuTitle {
    font-size: 14px;
    font-weight: 600;
    padding-left: 11px;
    height: 32px;
    color: #000;
    line-height: 22px;
    margin-bottom: 16px;
}

.menuIcon {
    font-size: 12px;
    padding-left: 8px;
    color: #b8b8bc
}

.runSetting {
    float: left;
    cursor: pointer;

    .reRunSettingBtn,
    .runSettingBtn {
        margin-left: 5px;
        padding: 3px 5px;
        font-size: 12px;
        background-color: var(--primary-color);
        border-radius: 3px;
        color: #fff;
    }

    .reRunSettingBtn {
        background-color: var(--error-color);
    }

    :global {
        .ant-select-selection-item {
            font-size: 12px !important;
        }

        .ant-select-selection-placeholder {
            font-size: 12px !important;
        }
    }
}

.runSettingEnv {
    width: 150px;
    padding: 5px;
}

.runSettingTitle {
    padding-bottom: 5px;
    margin-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
    font-weight: bold;
}

.runSettingBtn {
    float: right;
    cursor: pointer;
}

.deviceOperator {
    float: left;
    width: 200px;
    overflow: hidden;
    color: var(--color);
    border-bottom: 1px solid var(--border-color);
}