import { useRef, useEffect } from 'react';
import { Dropdown,Avatar} from 'antd';
import {
    UserOutlined,
    LogoutOutlined,
    FileSyncOutlined,
    MacCommandOutlined
} from '@ant-design/icons';
import { useLocation } from 'umi';
import { connectModel } from 'COMMON/middleware';
import electron from 'COMMON/utils/electron';
import baseModel from 'COMMON/models/baseModel';
import logoImg from 'RESOURCES/img/icon.png';
import ragImg from 'RESOURCES/img/qeRagIcon.png';
import SettingModal from 'FEATURES/components/Modal/SettingModal';

import { isElectron } from '../../../utils';
import styles from './index.module.less';

function PersonalCenter(props) {
    const {
        username,
        version,
        ragUserInfo,
        ragUsername,
        ragUserImage,
        ragUserDepartment,
        getRagUserInfo
    } = props;
    const personalCenterSettingModalRef = useRef(null);
    const location = useLocation();
    // 检查是否为RAG路由
    const isRagRoute = location.pathname.includes('/qe_rag');
    const isRagAuthenticated = ragUserInfo !== null;
    // 在RAG路由下自动获取用户信息
    // useEffect(() => {

    //     if (isRagRoute && !ragUserInfo) {
    //         console.log('PersonalCenter: 检测到RAG路由，开始获取用户信息...');
    //         if (getRagUserInfo) {
    //             getRagUserInfo();
    //         } else {
    //             console.error('getRagUserInfo action 不可用');
    //         }
    //     }
    // }, [isRagRoute, ragUserInfo, getRagUserInfo]);

    // 根据当前路由 展示用户信息
    const displayUsername = isRagRoute && isRagAuthenticated ? ragUsername : username;
    const displayUserImage = isRagRoute && isRagAuthenticated ? ragUserImage : null;

    const onClick = (e) => {
        const { keyPath } = e;
        if (keyPath.at(-1) === 'task') {
            personalCenterSettingModalRef?.current?.show({ key: 'task-center' });
        }
        if (keyPath.at(-1) === 'shortcut') {
            personalCenterSettingModalRef?.current?.show({ key: 'shortcut-center' });
        }
        if (keyPath.at(-1) === 'logout') {
            electron.send('system.login.exit');
        }
    };

    const items = [
        {
            key: 'qamate',
            label: (
                <span className={styles.personalCenterItem}>
                    {!isRagRoute ? (
                        <>
                            <img src={logoImg} width={16} className={styles.personalCenterIcon} />
                            QAMate v{version.qamate}
                        </>
                    ) : (
                        <>
                            <img src={ragImg} width={16} className={styles.personalCenterIcon} />
                            QE-RAG
                        </>
                    )}
                </span>
            )
        },
        {
            key: 'username',
            label: (
                <span className={styles.personalCenterItem}>
                    <UserOutlined className={styles.personalCenterIcon} />
                    {displayUsername}
                </span>
            )
        },
        {
            key: 'logout',
            disabled: !isElectron(),
            label: (
                <span className={styles.personalCenterItem}>
                    <LogoutOutlined className={styles.personalCenterIcon} />
                    退出登录
                </span>
            )
        },
        {
            type: 'divider',
            disabled: isRagRoute
        },
        {
            key: 'task',
            disabled: isRagRoute,
            label: (
                <span className={styles.personalCenterItem}>
                    <FileSyncOutlined className={styles.personalCenterIcon} />
                    任务栏
                </span>
            )
        },
        {
            key: 'shortcut',
            disabled: isRagRoute,
            label: (
                <span className={styles.personalCenterItem}>
                    <MacCommandOutlined className={styles.personalCenterIcon} />
                    快捷键
                </span>
            )
        }
    ];

    return (
        <>
            <Dropdown
                menu={{
                    items: items.filter((item) => !item.disabled),
                    onClick: onClick
                }}
            >
                <span className={styles.userInfo}>
                    <UserOutlined className={styles.userInfoIcon} />
                </span>
            </Dropdown>
            <SettingModal ref={personalCenterSettingModalRef} />
        </>
    );
}

export default connectModel([baseModel], (state) => ({
    version: state.common.base.version,
    spaceList: state.common.base.spaceList,
    username: state.common.base.username,
    showModal: state.common.base.showModal,
    currentSpace: state.common.base.currentSpace,
    currentModule: state.common.base.currentModule,
    // RAG用户信息
    ragUserInfo: state.common.base.ragUserInfo,
    ragUsername: state.common.base.ragUsername,
    ragUserImage: state.common.base.ragUserImage,
    ragUserDepartment: state.common.base.ragUserDepartment
}))(PersonalCenter);
