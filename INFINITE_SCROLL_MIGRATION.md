# 无限滚动迁移说明

## 概述

将知识库页面的传统分页逻辑改为下拉滚动到底部加载下一页的无限滚动模式，并封装成可复用的组件。

## 新增文件

### 1. `src/hooks/useInfiniteScroll.js`
- 无限滚动的核心Hook
- 处理数据加载、滚动监听、状态管理
- 支持参数更新和数据刷新

### 2. `src/common/components/InfiniteScrollContainer/`
- `InfiniteScrollContainer.jsx` - 可复用的无限滚动容器组件
- `InfiniteScrollContainer.module.less` - 组件样式
- `index.js` - 导出文件
- `README.md` - 使用文档
- `demo.jsx` - 使用示例
- `InfiniteScrollContainer.test.jsx` - 单元测试

## 修改文件

### 1. `src/features/qe_rag/knowledge/KnowledgePage.jsx`

**主要变更：**
- 移除了传统的分页组件 (`Pagination`)
- 移除了分页相关的状态管理 (`pagination`, `handlePageChange`)
- 重构了数据获取逻辑，创建了 `fetchBooksData` 函数适配无限滚动
- 使用 `InfiniteScrollContainer` 组件替换原有的卡片列表渲染
- 添加了 `infiniteScrollRef` 用于控制无限滚动组件
- 重构了搜索和标签切换逻辑，使用 `updateParams` 方法

**具体修改：**
- 导入了 `InfiniteScrollContainer` 组件
- 移除了 `useEffect`, `Spin`, `Pagination` 等不再需要的导入
- 简化了状态管理，移除了 `loading`, `books`, `pagination` 状态
- 重构了 `handleSearch` 和 `handleTabChange` 函数
- 修改了 `handleDelete` 函数，支持传入刷新回调
- 重构了 `renderKnowledgeCard` 函数，接收刷新方法作为参数

### 2. `src/features/qe_rag/knowledge/KnowledgePage.module.less`

**新增样式：**
- `.emptyContainer` - 空状态容器样式
- `.emptyMessage` - 空状态消息样式

## 功能特性

### 1. 无限滚动
- 滚动到距离底部100px时自动加载下一页
- 支持自定义触发阈值
- 防止重复加载的保护机制

### 2. 状态管理
- 自动处理加载状态
- 错误状态处理和重试机制
- 空状态展示

### 3. 参数更新
- 支持搜索参数实时更新
- 标签切换时自动刷新数据
- 工作组筛选联动

### 4. 性能优化
- 使用 `useCallback` 优化函数重新创建
- 防抖滚动事件处理
- 引用缓存避免不必要的重新渲染

## 使用方法

### 基本使用
```jsx
<InfiniteScrollContainer
    fetchData={fetchBooksData}
    renderItem={(item, index, { refresh }) => renderItem(item, refresh)}
    scrollOptions={{
        pageSize: 20,
        initialParams: { category: 'all' },
        threshold: 100
    }}
/>
```

### 高级使用
```jsx
const scrollRef = useRef(null);

// 更新搜索参数
const handleSearch = (params) => {
    scrollRef.current?.updateParams(params);
};

// 手动刷新
const handleRefresh = () => {
    scrollRef.current?.refresh();
};

<InfiniteScrollContainer
    ref={scrollRef}
    fetchData={fetchData}
    renderItem={renderItem}
    // ... 其他配置
/>
```

## 兼容性说明

- 保持了原有的UI界面和交互逻辑
- 搜索、筛选、标签切换功能完全兼容
- 新建、编辑、删除操作正常工作
- 响应式设计保持不变

## 测试建议

1. 测试基本的滚动加载功能
2. 测试搜索和筛选功能
3. 测试标签切换功能
4. 测试新建、编辑、删除操作
5. 测试错误处理和重试机制
6. 测试空状态展示
7. 测试响应式布局

## 后续优化建议

1. 添加虚拟滚动支持大量数据
2. 添加骨架屏提升用户体验
3. 支持预加载下一页数据
4. 添加滚动位置记忆功能
5. 支持自定义加载动画
