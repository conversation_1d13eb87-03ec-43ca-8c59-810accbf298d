# RAG认证系统集成文档

## 概述

本文档描述了如何将旧项目（rag-fe_副本）的登录逻辑迁移到主项目React中。当路由包含`qe_rag`时，系统会自动使用RAG的认证逻辑获取用户信息。

## 核心功能

### 1. 路由判断
- 自动检测当前路由是否包含`qe_rag`
- 仅在RAG相关路由下启用RAG认证逻辑
- 其他路由继续使用原有的token认证系统

### 2. 用户信息获取
- 通过`/rag/api/user/current`接口获取用户信息
- 支持重定向处理（code 40001）
- 自动存储用户信息到sessionStorage
- 提供默认用户信息fallback

### 3. 状态管理
- **Redux集成**：RAG用户信息存储在baseModel中
- 支持异步获取、同步设置、清除操作
- 与现有Redux状态管理系统无缝集成
- 路由变化时自动更新认证状态

### 4. UI集成
- **PersonalCenter组件**：自动在RAG路由下显示RAG用户信息
- 用户头像动态切换（RAG用户头像 vs 默认头像）
- 下拉菜单显示用户部门和RAG模式标识
- 头像旁显示"RAG"标识

## 文件结构

```
src/
├── common/models/
│   └── baseModel.js                # **RAG状态管理** ⭐ 核心状态
├── layouts/BasicLayout/
│   ├── utils/
│   │   └── ragAuth.js              # RAG认证核心逻辑
│   ├── BasicLayout.jsx             # 集成RAG认证到主布局
│   ├── utils.js                    # 工具函数
│   └── LayoutHeader/components/
│       └── PersonalCenter/
│           └── index.jsx           # 集成RAG用户信息显示
├── hooks/
│   └── useRagAuth.js               # 简化的RAG Hook（仅路由判断）
├── components/
│   └── RagUserInfo/                # RAG用户信息组件
│       ├── RagUserInfo.jsx
│       └── RagUserInfo.module.less
└── features/qe_rag/test/           # 测试组件
    ├── ReduxRagTest.jsx            # Redux状态管理测试
    └── PersonalCenterTest.jsx
```

## 使用方法

### 1. 在组件中使用Redux状态管理

```jsx
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';

function MyRagComponent(props) {
    const {
        ragUserInfo,           // 用户信息对象
        ragUsername,          // 用户名
        ragUserImage,         // 用户头像
        ragUserDepartment,    // 用户部门
        getRagUserInfo,       // 获取用户信息action
        clearRagUserInfo      // 清除用户信息action
    } = props;

    const isRagRoute = window.location.pathname.includes('/qe_rag');
    const isRagAuthenticated = ragUserInfo !== null;

    if (!isRagRoute) {
        return null; // 非RAG路由不显示
    }

    if (!isRagAuthenticated) {
        return <div>未登录</div>;
    }

    return (
        <div>
            <h1>欢迎, {ragUsername}!</h1>
            <p>部门: {ragUserDepartment}</p>
            <button onClick={getRagUserInfo}>刷新</button>
        </div>
    );
}

export default connectModel([baseModel], (state) => ({
    ragUserInfo: state.common.base.ragUserInfo,
    ragUsername: state.common.base.ragUsername,
    ragUserImage: state.common.base.ragUserImage,
    ragUserDepartment: state.common.base.ragUserDepartment
}))(MyRagComponent);
```

### 2. 使用预制的用户信息组件

```jsx
import RagUserInfo from 'COMPONENTS/RagUserInfo/RagUserInfo';

function MyPage() {
    return (
        <div>
            {/* 基础用法 */}
            <RagUserInfo />
            
            {/* 自定义配置 */}
            <RagUserInfo 
                showDepartment={false}
                showRefresh={true}
                size="small"
                layout="vertical"
            />
        </div>
    );
}
```

## API参考

### ragAuth.js 核心函数

#### `isRagRoute(pathname)`
检查路由是否为RAG相关路由
- **参数**: `pathname` - 当前路径
- **返回**: `boolean` - 是否为RAG路由

#### `getRagUserInfo()`
获取RAG用户信息
- **返回**: `Promise<Object|null>` - 用户信息对象

#### `ragRouteGuard(pathname)`
RAG路由守卫，确保RAG路由下有用户信息
- **参数**: `pathname` - 当前路径
- **返回**: `Promise<Object|null>` - 用户信息或null

### Redux状态管理

#### baseModel中的RAG状态

| 状态字段 | 类型 | 描述 |
|---------|------|------|
| `ragUserInfo` | `Object\|null` | 完整的用户信息对象 |
| `ragUsername` | `string` | 用户名 |
| `ragUserImage` | `string` | 用户头像URL |
| `ragUserDepartment` | `string` | 用户部门 |

#### 可用的Actions

| Action | 参数 | 描述 |
|--------|------|------|
| `getRagUserInfo` | 无 | 异步获取RAG用户信息 |
| `setRagUserInfo` | `ragUserInfo` | 同步设置用户信息 |
| `clearRagUserInfo` | 无 | 清除所有RAG用户信息 |
| `setRagUsername` | `ragUsername` | 设置用户名 |
| `setRagUserImage` | `ragUserImage` | 设置用户头像 |
| `setRagUserDepartment` | `ragUserDepartment` | 设置用户部门 |

### useRagAuth Hook（简化版）

返回对象包含以下属性：

| 属性 | 类型 | 描述 |
|------|------|------|
| `isRagRoute` | `boolean` | 当前是否为RAG路由 |

## 存储机制

RAG用户信息存储在sessionStorage中：
- `rag_username` - 用户名
- `rag_imageUrl` - 用户头像URL
- `rag_departmentName` - 用户部门名称

## 错误处理

1. **网络错误**: 自动使用默认用户信息
2. **认证失败**: 显示错误提示，提供重试功能
3. **重定向**: 自动处理40001状态码的重定向

## 注意事项

1. RAG认证仅在包含`qe_rag`的路由下生效
2. 用户信息存储在sessionStorage中，页面刷新后会保持
3. 路由切换时会自动检查和更新认证状态
4. 支持与现有token系统并存，不会相互干扰

## 主要集成点

### PersonalCenter组件集成
PersonalCenter组件（位于`src/layouts/BasicLayout/LayoutHeader/components/PersonalCenter/index.jsx`）已经集成了RAG认证功能：

1. **自动用户信息切换**：在RAG路由下自动显示RAG用户信息
2. **头像显示**：支持RAG用户头像，fallback到默认图标
3. **下拉菜单增强**：显示用户部门和RAG模式标识
4. **视觉标识**：在RAG路由下显示"RAG"标识

### 使用效果
- **非RAG路由**：显示普通用户信息和默认头像
- **RAG路由**：显示RAG用户信息、真实头像、部门信息和RAG标识

## 迁移检查清单

- [x] 创建RAG认证核心逻辑 (`ragAuth.js`)
- [x] 集成到BasicLayout组件
- [x] 创建useRagAuth Hook
- [x] 创建RagUserInfo组件
- [x] **集成到PersonalCenter组件** ⭐ 主要集成点
- [x] 添加CSS样式
- [x] 创建测试组件
- [x] 编写使用文档

## 测试建议

1. 访问非RAG路由，确认不会触发RAG认证
2. 访问RAG路由，确认能正确获取用户信息
3. 测试网络异常情况下的fallback机制
4. 测试路由切换时的状态更新
5. 测试用户信息刷新功能
